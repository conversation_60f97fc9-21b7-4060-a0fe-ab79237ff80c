from enum import StrEnum
from typing import Any, Dict, List

from pydantic import BaseModel


class ModelTokenLimit(BaseModel):
    total: int
    output: int | None = None


class ModelDeployments(StrEnum):
    GPT_4_8K = 'gpt-4'
    GPT_4_32K = 'gpt-4-32k'
    GPT_4_128K = 'gpt-4-128k'
    GPT_4_O = 'gpt-4o'


class ModelSetting(BaseModel):
    deployment: ModelDeployments
    name: str
    limit: ModelTokenLimit


class ModelsSettings:
    MODELS: list[ModelSetting] = [
        ModelSetting(
            deployment=ModelDeployments.GPT_4_8K,
            name='GPT-4',
            limit=ModelTokenLimit(total=8192),
        ),
        ModelSetting(
            deployment=ModelDeployments.GPT_4_32K,
            name='GPT-4 32K',
            limit=ModelTokenLimit(total=32768),
        ),
        ModelSetting(
            deployment=ModelDeployments.GPT_4_128K,
            name='GPT-4 128K Output 4K',
            limit=ModelTokenLimit(total=128000, output=4096),
        ),
        ModelSetting(
            deployment=ModelDeployments.GPT_4_O,
            name='GPT-4o',
            limit=ModelTokenLimit(total=128000, output=4096),
        ),
    ]

    CHUNK_TO_OVERLAP: float = 0.1
    INPUT_TO_OUTPUT: float = 0.1

    MODELS_BY_DEPLOYMENT: dict[ModelDeployments, ModelSetting] = {model.deployment: model for model in MODELS}

    def get_settings(self, deployment: ModelDeployments = ModelDeployments.GPT_4_O) -> ModelSetting:
        if deployment not in self.MODELS_BY_DEPLOYMENT:
            raise ValueError(f'Unknown model deployment {deployment}.')

        return self.MODELS_BY_DEPLOYMENT[deployment]

    def text_to_chunks(
        self,
        content: str,
        chunk_size: int,
        overlap: int,
        model_settings: ModelSetting,
        metadata: Dict[str, Any] | None = None,
    ) -> List[Dict[str, Any]]:
        """
        Split text content into chunks using the specified parameters.

        Args:
            content: The text content to chunk
            chunk_size: Maximum size of each chunk in tokens
            overlap: Number of tokens to overlap between chunks
            model_settings: Model settings containing deployment information
            metadata: Optional metadata to include with each chunk

        Returns:
            List of chunks, each containing the text and metadata
        """
        from .chunking import RecursiveChunkingStrategy

        # Use the RecursiveChunkingStrategy with dynamic parameters
        # Get the correct encoding name for the model
        import tiktoken
        try:
            encoding = tiktoken.encoding_for_model(model_settings.deployment.value)
            encoding_name = encoding.name
        except KeyError:
            # Fallback to cl100k_base for newer models like gpt-4o
            encoding_name = 'cl100k_base'

        chunking_strategy = RecursiveChunkingStrategy(
            chunk_size=chunk_size,
            chunk_overlap=overlap,
            encoding_name=encoding_name,
        )

        # Prepare metadata
        chunk_metadata = metadata or {}

        return chunking_strategy.chunk_document(content, chunk_metadata)


# Example usage (commented for reference):
# current_chunk_tokens_count = calculate_tokens(
#     model_name=model_deployment, text=chunk
# )
# chunk_size, overlap_size, text_total, max_tokens = get_chunks_size(
#     system_prompt=empty_system_prompt_tokens,
#     token_limits=ModelTokenLimit(total=int(current_chunk_tokens_count * 0.8)),
#     input_to_output=0.2,
#     chunk_to_overlap=0.1,
#     max_user_length_percent=95,
# )

# return self.text_to_chunks(
#     content=chunk,
#     chunk_size=chunk_size,
#     overlap=overlap_size,
#     model_settings=model_settings,
# )