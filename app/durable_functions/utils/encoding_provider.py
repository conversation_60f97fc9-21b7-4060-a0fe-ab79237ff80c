import tiktoken


encoding_cache: dict[str, tiktoken.Encoding] = {}


def get_encoding(model_name: str):
    encoding = encoding_cache.get(model_name, None)
    if encoding:
        return encoding

    try:
        encoding = tiktoken.encoding_for_model(model_name)
    except KeyError:
        # Fallback to cl100k_base for newer models like gpt-4o
        encoding = tiktoken.get_encoding('cl100k_base')

    encoding_cache[model_name] = encoding
    return encoding


def calculate_tokens(model_name: str, text: str) -> int:
    encoding = get_encoding(model_name)
    return len(encoding.encode(text))
